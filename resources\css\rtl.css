/* RTL Support */
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
}

.rtl .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
}

/* Form inputs RTL */
.rtl input,
.rtl textarea {
    text-align: right;
}

/* Tables RTL */
.rtl table {
    text-align: right;
}

/* Flex direction for RTL */
.rtl .flex-row {
    flex-direction: row-reverse;
}

/* Margins and Paddings */
.rtl .mr-1 {
    margin-left: 0.25rem;
    margin-right: 0;
}
.rtl .mr-2 {
    margin-left: 0.5rem;
    margin-right: 0;
}
.rtl .mr-3 {
    margin-left: 0.75rem;
    margin-right: 0;
}
.rtl .mr-4 {
    margin-left: 1rem;
    margin-right: 0;
}

.rtl .ml-1 {
    margin-right: 0.25rem;
    margin-left: 0;
}
.rtl .ml-2 {
    margin-right: 0.5rem;
    margin-left: 0;
}
.rtl .ml-3 {
    margin-right: 0.75rem;
    margin-left: 0;
}
.rtl .ml-4 {
    margin-right: 1rem;
    margin-left: 0;
}

.rtl .pr-1 {
    padding-left: 0.25rem;
    padding-right: 0;
}
.rtl .pr-2 {
    padding-left: 0.5rem;
    padding-right: 0;
}
.rtl .pr-3 {
    padding-left: 0.75rem;
    padding-right: 0;
}
.rtl .pr-4 {
    padding-left: 1rem;
    padding-right: 0;
}

.rtl .pl-1 {
    padding-right: 0.25rem;
    padding-left: 0;
}
.rtl .pl-2 {
    padding-right: 0.5rem;
    padding-left: 0;
}
.rtl .pl-3 {
    padding-right: 0.75rem;
    padding-left: 0;
}
.rtl .pl-4 {
    padding-right: 1rem;
    padding-left: 0;
}

/* Border radius adjustments for RTL */
.rtl .rounded-l {
    border-radius: 0 0.25rem 0.25rem 0;
}
.rtl .rounded-r {
    border-radius: 0.25rem 0 0 0.25rem;
}

/* Icons and buttons */
.rtl .icon {
    transform: scaleX(-1);
}

/* Dropdown menus */
.rtl .dropdown-menu {
    right: auto;
    left: 0;
}

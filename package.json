{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.0.0", "@inertiajs/react": "^2.0.3", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^3.4.17", "tailwindcss-rtl": "^0.9.0", "vite": "^6.0.11"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "chart.js": "^4.4.9", "framer-motion": "^12.5.0", "leaflet": "^1.9.4", "react-chartjs-2": "^5.3.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-router-dom": "^7.2.0", "swiper": "^11.2.6"}}
<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): Response
    {
        $user = $request->user();

        // Check if user has permission to access profile settings
        if (!$user || !($user->isAdmin() || $user->isTailor() || $user->isShopkeeper())) {
            abort(403, 'Unauthorized access to profile settings.');
        }

        return Inertia::render('Profile/Edit', [
            'mustVerifyEmail' => $user instanceof MustVerifyEmail,
            'status' => session('status'),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $user = $request->user();

        // Check if user has permission to update profile
        if (!$user || !($user->isAdmin() || $user->isTailor() || $user->isShopkeeper())) {
            abort(403, 'Unauthorized access to profile settings.');
        }

        $user->fill($request->validated());

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();

        return Redirect::route('profile.edit')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Check if user has permission to delete profile
        if (!$user || !($user->isAdmin() || $user->isTailor() || $user->isShopkeeper())) {
            abort(403, 'Unauthorized access to profile settings.');
        }

        $request->validate([
            'password' => ['required', 'current_password'],
        ], [
            'password.required' => 'پټنوم ضروری دی',
            'password.current_password' => 'پټنوم سم نه دی',
        ]);

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}

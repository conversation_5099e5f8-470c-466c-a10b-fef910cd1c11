<?php

namespace App\Policies;

use App\Models\Cloth;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ClothPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Allow viewing list only if authenticated
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Cloth $cloth): bool
    {
        // User can only view their own clothes
        return $user->id === $cloth->user_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->isTailor();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Cloth $cloth): bool
    {
        // User can only update their own clothes
        return $user->id === $cloth->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Cloth $cloth): bool
    {
        // User can only delete their own clothes
        return $user->id === $cloth->user_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Cloth $cloth): bool
    {
        // User can only restore their own clothes
        return $user->id === $cloth->user_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Cloth $cloth): bool
    {
        // User can only force delete their own clothes
        return $user->id === $cloth->user_id;
    }
}
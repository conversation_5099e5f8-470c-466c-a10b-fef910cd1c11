<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;
use App\Models\TailorPost;
use App\Models\PostRating;
use App\Models\CustomerOrder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SiteController extends Controller
{
    public function tailors()
    {
        // Get all tailors regardless of whether they have a shop or not
        $tailors = User::where('role', 'Tailor')
            ->select([
                'id',
                'name',
                'email',
                'profile_image',
                'experience',
                'career',
                'previous_work',
                'certifications',
                'skills',
                'work_availability',
                'tailoring_name',
                'created_at'
            ])
            ->with(['posts' => function($query) {
                $query->withAvg('ratings', 'rating');
            }])
            ->get()
            ->map(function ($tailor) {
                // Calculate Amazon-style tailor rating from all post ratings
                $allRatings = collect();

                foreach ($tailor->posts as $post) {
                    // Get all individual ratings for this post
                    $postRatings = \App\Models\PostRating::where('tailor_post_id', $post->id)->get();
                    $allRatings = $allRatings->merge($postRatings);
                }

                // Calculate Amazon-style Bayesian average
                if ($allRatings->isEmpty()) {
                    $ratingPercentage = 0;
                    $credibilityScore = 0;
                    $totalRatings = 0;
                } else {
                    $totalRatings = $allRatings->count();
                    $rawSum = $allRatings->sum('rating');
                    $rawAverage = $rawSum / $totalRatings;

                    // Amazon-style Bayesian average
                    $globalMean = 3.5; // Global average
                    $minimumRatings = 5; // Minimum for credibility

                    $bayesianAverage = (
                        ($totalRatings * $rawAverage) + ($minimumRatings * $globalMean)
                    ) / ($totalRatings + $minimumRatings);

                    $ratingPercentage = ($bayesianAverage / 5) * 100;
                    $credibilityScore = min(100, ($totalRatings / 10) * 100);
                }

                // Force refresh the tailor data and get order statistics
                $tailor->refresh();
                $orderStats = $tailor->getOrderStatistics();

                // Debug log for the specific tailor with weekly_order_limit = 100
                if ($tailor->weekly_order_limit == 100) {
                    Log::info('Tailor Order Stats Debug:', [
                        'tailor_id' => $tailor->id,
                        'tailor_name' => $tailor->name,
                        'weekly_order_limit' => $tailor->weekly_order_limit,
                        'current_week_orders' => $tailor->current_week_orders,
                        'order_stats' => $orderStats
                    ]);
                }

                // Get credibility level text
                $getCredibilityLevel = function($score) {
                    if ($score >= 80) return 'ډیر باوري'; // Very Reliable
                    if ($score >= 60) return 'باوري'; // Reliable
                    if ($score >= 40) return 'منځنی'; // Medium
                    if ($score >= 20) return 'لږ باوري'; // Less Reliable
                    return 'جدید'; // New
                };

                return [
                    'id' => $tailor->id,
                    'name' => $tailor->name,
                    'email' => $tailor->email,
                    'profile_photo_url' => $tailor->profile_image ? Storage::url($tailor->profile_image) : null,
                    'experience' => $tailor->experience,
                    'career' => $tailor->career,
                    'previous_work' => $tailor->previous_work,
                    'certifications' => $tailor->certifications,
                    'skills' => $tailor->skills,
                    'work_availability' => $tailor->work_availability,
                    'tailoring_name' => $tailor->tailoring_name,
                    'created_at' => $tailor->created_at,
                    'rating_percentage' => round($ratingPercentage, 1),
                    'total_ratings' => $totalRatings ?? 0,
                    'credibility_score' => round($credibilityScore ?? 0),
                    'credibility_level' => $getCredibilityLevel($credibilityScore ?? 0),
                    'order_statistics' => $orderStats
                ];
            });

        return Inertia::render('Site/Tailors', [
            'tailors' => $tailors
        ]);
    }

    public function shops()
    {
        // Get only tailors who have shops
        $shops = User::where('role', 'Tailor')
            ->whereNotNull('tailoring_name')
            ->select([
                'id',
                'name',
                'email',
                'profile_image',
                'tailoring_name',
                'tailoring_address',
                'tailor_count',
                'contact_number',
                'shop_email',
                'working_hours',
                'services',
                'payment_methods',
                'social_links',
                'shop_images',
                'published_year',
                'created_at'
            ])
            ->get()
            ->map(function ($shop) {
                // Debug profile image path
                $profileImagePath = $shop->profile_image;
                $profileImageExists = $profileImagePath ? Storage::disk('public')->exists($profileImagePath) : false;

                // Get shop_images - model casting should handle JSON conversion
                $shopImages = $shop->shop_images;

                // Ensure it's an array (fallback for edge cases)
                if (!is_array($shopImages)) {
                    $shopImages = $shopImages ? [$shopImages] : [];
                }

                return [
                    'id' => $shop->id,
                    'name' => $shop->name,
                    'email' => $shop->email,
                    'profile_image' => $profileImageExists ? $profileImagePath : null,
                    'tailoring_name' => $shop->tailoring_name,
                    'tailoring_address' => $shop->tailoring_address,
                    'tailor_count' => $shop->tailor_count,
                    'contact_number' => $shop->contact_number,
                    'shop_email' => $shop->shop_email,
                    'working_hours' => $shop->working_hours,
                    'services' => $shop->services,
                    'payment_methods' => $shop->payment_methods,
                    'social_links' => $shop->social_links,
                    'shop_images' => $shopImages,
                    'published_year' => $shop->published_year,
                    'created_at' => $shop->created_at
                ];
            });

        return Inertia::render('Site/Shop', [
            'shops' => $shops
        ]);
    }

    public function posts()
    {
        $currentUserId = Auth::id();

        $posts = TailorPost::with(['user', 'ratings'])
            ->latest()
            ->get()
            ->map(function ($post) use ($currentUserId) {
                $averageRating = $post->ratings->avg('rating') ?? 0;
                // Use stored comments count for better performance
                $commentsCount = $post->comments;

                // Check if current user has rated this post
                $userHasRated = false;
                if ($currentUserId) {
                    $userHasRated = $post->ratings->where('user_id', $currentUserId)->isNotEmpty();
                }

                return [
                    'id' => $post->id,
                    'description' => $post->description,
                    'image' => $post->image ? asset('storage/' . $post->image) : null,
                    'date' => $post->date,
                    'author' => $post->user->name ?? $post->author,
                    'email' => $post->email,
                    'category' => $post->category,
                    'comments' => $commentsCount,
                    'rating' => $averageRating,
                    'user_has_rated' => $userHasRated,
                    'created_at' => $post->created_at
                ];
            });

        // Get list of post IDs that current user has rated
        $userRatedPosts = [];
        if ($currentUserId) {
            $userRatedPosts = PostRating::where('user_id', $currentUserId)
                ->pluck('tailor_post_id')
                ->toArray();
        }

        return Inertia::render('Site/Posts', [
            'tailorPosts' => $posts,
            'userRatedPosts' => $userRatedPosts
        ]);
    }

    public function home()
    {
        // Get top 10 rated posts with Amazon-style ratings
        $topDesigns = TailorPost::with(['ratings'])
            ->get()
            ->map(function ($post) {
                $ratingData = $post->calculateAmazonStyleRating();

                return [
                    'id' => $post->id,
                    'description' => $post->description,
                    'image' => $post->image ? asset('storage/' . $post->image) : null,
                    'category' => $post->category,
                    'averageRating' => (float)$ratingData['display_rating'], // Amazon-style rating
                    'rawRating' => (float)$ratingData['raw_average'], // Simple average
                    'totalRatings' => $ratingData['total_ratings'],
                    'credibilityScore' => $ratingData['credibility_score'],
                    'percentage' => $ratingData['percentage'],
                    'credibilityLevel' => $post->getCredibilityLevel(),
                    'ratings' => $post->ratings->map(function($rating) {
                        return [
                            'id' => $rating->id,
                            'rating' => (float)$rating->rating // Ensure it's a number
                        ];
                    })
                ];
            })
            ->filter(function ($post) {
                return $post['averageRating'] > 0 && $post['image']; // Only show posts with ratings and images
            })
            ->sortByDesc('averageRating')
            ->take(10)
            ->values();

        // Debug logging
        Log::info('Top Designs Data:', [
            'count' => $topDesigns->count(),
            'designs' => $topDesigns->toArray()
        ]);

        // Get testimonials (ratings with comments)
        $testimonialsWithComments = PostRating::with(['user', 'post'])
            ->whereNotNull('comment')
            ->orderByDesc('created_at')
            ->take(15)
            ->get()
            ->map(function ($rating) {
                return [
                    'id' => $rating->id,
                    'username' => $rating->user->name ?? 'Anonymous',
                    'userImage' => $rating->user->profile_photo_url ?? null,
                    'rating' => $rating->rating,
                    'comment' => $rating->comment,
                    'postId' => $rating->post_id,
                    'created_at' => $rating->created_at->format('Y-m-d')
                ];
            });

        return Inertia::render('Site/Home', [
            'topDesigns' => $topDesigns,
            'testimonialsWithComments' => $testimonialsWithComments,
        ]);
    }

    public function order(Request $request)
    {
        $tailorId = $request->get('tailorId');
        $tailorName = $request->get('tailorName');

        $orderStatistics = null;
        $userHasOrderedThisWeek = false;

        // Check if user has already ordered this week
        if (Auth::check()) {
            $userHasOrderedThisWeek = Auth::user()->hasOrderedThisWeek();
        }

        // If tailor is selected, get their order statistics
        if ($tailorId) {
            $tailor = User::find($tailorId);
            if ($tailor && $tailor->isTailor()) {
                $orderStatistics = $tailor->getOrderStatistics();
            }
        }

        return Inertia::render('Site/Order', [
            'tailorId' => $tailorId,
            'tailorName' => $tailorName,
            'orderStatistics' => $orderStatistics,
            'userHasOrderedThisWeek' => $userHasOrderedThisWeek,
        ]);
    }
}
